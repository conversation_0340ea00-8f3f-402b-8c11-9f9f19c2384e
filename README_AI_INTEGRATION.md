# AI Agent Integration

## Overview
Simple, production-grade integration untuk mengirim request payload ke AI Agent.

## Request Format
```json
{
  "userId": "user001",
  "sessionId": "session_0001",
  "message": "berikan list semua product category"
}
```

## URL Endpoint
Default: `https://n8n.finityhub.ai/webhook-test/fmcg`

Dapat dikonfigurasi via environment variable:
```env
AI_AGENT_URL=https://n8n.finityhub.ai/webhook-test/fmcg
```

## Core Files

### `lib/schemas.ts`
- Zod validation schema
- Type definitions
- ID generators

### `lib/session.ts`
- Session management
- localStorage persistence
- Auto-generate userId & sessionId

### `lib/api-client.ts`
- Simple function untuk kirim request ke AI Agent
- Zod validation
- Error handling

### `app/api/chat/route.ts`
- API endpoint `/api/chat`
- Parse request dari client
- <PERSON><PERSON> ke AI Agent
- Return response

### `components/SessionInfo.tsx`
- Display session information
- Reset session functionality

## Usage

1. **Install dependencies:**
   ```bash
   npm install zod
   ```

2. **Run application:**
   ```bash
   npm run dev
   ```

3. **Test:**
   - Open http://localhost:3000
   - Send message: "berikan list semua product category"
   - Check browser console for session info

## Production Ready Features

- ✅ Zod validation
- ✅ Error handling
- ✅ TypeScript types
- ✅ Session persistence
- ✅ Clean code structure
- ✅ No unnecessary dependencies
- ✅ Simple & maintainable

"use client";

import { useState } from "react";
import {
  MessageSquare,
  Settings,
  User,
  MoreHorizontal,
  Search,
  Archive,
  Trash2,
} from "lucide-react";
import ThemeToggle from "./ThemeToggle";
import SessionInfo from "./SessionInfo";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const chatHistory: Array<{
    id: string;
    title: string;
    isActive: boolean;
    timestamp: string;
  }> = [];

  const filteredChats = chatHistory.filter((chat) =>
    chat.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed inset-y-0 left-0 z-50 w-72 h-full flex flex-col border-r bg-white dark:bg-gray-800 dark:border-gray-700 
          transition-transform duration-300 ease-in-out lg:relative lg:z-auto border-gray-200
          ${isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}`}
      >
        {/* Header */}
        <div className="p-3 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search
              size={14}
              className="absolute left-2.5 top-1/2 -translate-y-1/2 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-8 pr-3 py-1.5 text-xs rounded-md border bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 
              text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
            />
          </div>
        </div>

        {/* Session Info */}
        <div className="p-3">
          <SessionInfo />
        </div>

        {/* Chat History */}
        <div className="flex-1 overflow-y-auto p-3">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400">
              Recent chats
            </h3>
            <div className="flex gap-1">
              <button
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Archive"
              >
                <Archive size={12} className="text-gray-400" />
              </button>
              <button
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Delete"
              >
                <Trash2 size={12} className="text-gray-400" />
              </button>
            </div>
          </div>

          <div className="space-y-0.5">
            {filteredChats.length > 0 ? (
              filteredChats.map((chat) => (
                <div
                  key={chat.id}
                  className={`group flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors ${chat.isActive
                    ? "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700"
                    }`}
                >
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div
                      className={`w-1.5 h-1.5 rounded-full ${chat.isActive
                        ? "bg-blue-500"
                        : "bg-gray-300 dark:bg-gray-600"
                        }`}
                    />
                    <div className="min-w-0 flex-1">
                      <div
                        className={`truncate text-xs ${chat.isActive
                          ? "font-medium text-blue-900 dark:text-blue-100"
                          : "text-gray-900 dark:text-white"
                          }`}
                      >
                        {chat.title}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {chat.timestamp}
                      </div>
                    </div>
                  </div>
                  <button className="opacity-0 group-hover:opacity-100 p-0.5 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition">
                    <MoreHorizontal size={12} className="text-gray-400" />
                  </button>
                </div>
              ))
            ) : (
              <div className="py-6 text-center">
                <MessageSquare
                  size={24}
                  className="mx-auto mb-2 text-gray-300 dark:text-gray-600"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  No conversations found
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="h-16 p-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between h-full">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 flex items-center justify-center rounded-full bg-blue-500">
                <User size={14} className="text-white" />
              </div>
              <div>
                <div className="truncate text-xs font-medium text-gray-900 dark:text-white">
                  Guest
                </div>
                <div className="truncate text-xs text-gray-500 dark:text-gray-400">
                  Free plan
                </div>
              </div>
            </div>
            <div className="flex gap-1">
              <ThemeToggle />
              <button className="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                <Settings size={14} className="text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}

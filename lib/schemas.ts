import { z } from 'zod';

export const AIAgentRequestSchema = z.object({
  userId: z.string().min(1),
  sessionId: z.string().min(1),
  message: z.string().min(1),
});

export type AIAgentRequest = z.infer<typeof AIAgentRequestSchema>;

export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

export function generateUserId(): string {
  return `user_${Math.random().toString(36).substring(2, 11)}`;
}

'use client';

import { generateSessionId, generateUserId } from './schemas';

const STORAGE_KEY = 'fin_chat_session';

export interface SessionData {
  sessionId: string;
  userId: string;
}

export function getOrCreateSession(): SessionData {
  if (typeof window === 'undefined') {
    return {
      sessionId: generateSessionId(),
      userId: generateUserId(),
    };
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      return JSON.parse(stored);
    }

    const newSession: SessionData = {
      sessionId: generateSessionId(),
      userId: generateUserId(),
    };

    localStorage.setItem(STORAGE_KEY, JSON.stringify(newSession));
    return newSession;
  } catch {
    return {
      sessionId: generateSessionId(),
      userId: generateUserId(),
    };
  }
}

export function resetSession(): SessionData {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(STORAGE_KEY);
  }
  return getOrCreateSession();
}

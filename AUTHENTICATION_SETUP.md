# NextAuth.js with Google OAuth and Prisma Setup Guide

This guide provides complete setup instructions for implementing NextAuth.js authentication with Google OAuth and PostgreSQL database integration using Prisma.

## 🚀 Quick Start

### 1. Environment Variables Setup

Copy the `.env.example` file to `.env.local`:

```bash
cp .env.example .env.local
```

Update the following environment variables in `.env.local`:

```env
# Database Configuration
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/postgres"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-generate-a-strong-one"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### 2. Generate NextAuth Secret

Generate a secure secret for NextAuth.js:

```bash
openssl rand -base64 32
```

Replace `your-secret-key-here-generate-a-strong-one` with the generated secret.

### 3. Google OAuth Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Configure the OAuth consent screen
6. Set the application type to "Web application"
7. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)
8. Copy the Client ID and Client Secret to your `.env.local` file

### 4. Database Setup

#### Option A: Local PostgreSQL

1. Install PostgreSQL locally
2. Create a database:
   ```sql
   CREATE DATABASE fin_chat;
   ```
3. Update the `DATABASE_URL` in `.env.local`

#### Option B: Docker PostgreSQL

```bash
docker run --name postgres-fin-chat \
  -e POSTGRES_PASSWORD=postgres \
  -e POSTGRES_DB=postgres \
  -p 5432:5432 \
  -d postgres:15
```

### 5. Prisma Setup

Initialize and migrate the database:

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma db push

# Optional: Open Prisma Studio to view data
npx prisma studio
```

### 6. Install Dependencies

The required dependencies are already installed. If you need to reinstall:

```bash
npm install next-auth @next-auth/prisma-adapter prisma @prisma/client
```

### 7. Start the Application

```bash
npm run dev
```

Visit `http://localhost:3000` and you'll be redirected to the sign-in page.

## 📁 File Structure

```
├── app/
│   ├── api/auth/[...nextauth]/route.ts    # NextAuth.js API routes
│   ├── auth/
│   │   ├── signin/page.tsx                # Custom sign-in page
│   │   └── error/page.tsx                 # Authentication error page
│   ├── layout.tsx                         # Root layout with SessionProvider
│   └── page.tsx                           # Protected main page
├── components/
│   ├── ProtectedRoute.tsx                 # Route protection wrapper
│   ├── SessionProvider.tsx                # NextAuth session provider
│   ├── TopBar.tsx                         # Updated with user menu
│   └── Chat.tsx                           # Updated with user info
├── lib/
│   └── auth.ts                            # NextAuth.js configuration
├── prisma/
│   └── schema.prisma                      # Database schema
├── types/
│   └── next-auth.d.ts                     # TypeScript type extensions
└── .env.example                           # Environment variables template
```

## 🗄️ Database Schema

The Prisma schema includes the following tables:

- **users**: User profiles with custom fields (role, companyId)
- **accounts**: OAuth account connections
- **sessions**: Database sessions
- **verificationtokens**: Email verification tokens
- **companies**: Optional multi-tenant support

## 🔧 Configuration Details

### NextAuth.js Features

- **Database sessions**: Sessions stored in PostgreSQL
- **Google OAuth**: Secure authentication with Google accounts
- **Custom pages**: Branded sign-in and error pages
- **TypeScript support**: Full type safety
- **Session callbacks**: Custom user data in sessions
- **Role-based access**: User roles and company associations

### Security Features

- **CSRF protection**: Built-in CSRF token validation
- **Secure cookies**: HTTPOnly and Secure cookie flags
- **Session rotation**: Automatic session token rotation
- **Domain validation**: Optional email domain restrictions

## 🎨 Customization

### Adding Domain Restrictions

Uncomment and modify the domain restriction in `lib/auth.ts`:

```typescript
// Optional: Add domain restriction
const allowedDomains = ["yourcompany.com"]
if (user.email && !allowedDomains.some(domain => user.email!.endsWith(domain))) {
  return false
}
```

### Custom User Fields

The schema includes custom fields like `role` and `companyId`. You can extend these in:

1. `prisma/schema.prisma` - Add database fields
2. `types/next-auth.d.ts` - Add TypeScript types
3. `lib/auth.ts` - Include in session callbacks

## 🚨 Troubleshooting

### Common Issues

1. **"Invalid client" error**: Check Google OAuth credentials
2. **Database connection error**: Verify DATABASE_URL format
3. **NEXTAUTH_SECRET missing**: Ensure secret is set in environment
4. **Redirect URI mismatch**: Check Google OAuth redirect URIs

### Debug Mode

Enable debug mode in development:

```env
NODE_ENV=development
```

This enables detailed logging in the NextAuth.js configuration.

## 🔄 Production Deployment

### Environment Variables

Update for production:

```env
NEXTAUTH_URL="https://yourdomain.com"
DATABASE_URL="your-production-database-url"
NODE_ENV="production"
```

### Google OAuth

Add production redirect URI:
```
https://yourdomain.com/api/auth/callback/google
```

### Database Migration

Run migrations in production:

```bash
npx prisma migrate deploy
```

## 📚 Additional Resources

- [NextAuth.js Documentation](https://next-auth.js.org/)
- [Prisma Documentation](https://www.prisma.io/docs/)
- [Google OAuth Setup Guide](https://developers.google.com/identity/protocols/oauth2)

## 🆘 Support

If you encounter issues:

1. Check the browser console for errors
2. Review the server logs
3. Verify all environment variables are set
4. Ensure database is accessible
5. Check Google OAuth configuration

The authentication system is now fully configured and ready for use!

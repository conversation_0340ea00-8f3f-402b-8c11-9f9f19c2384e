import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      role?: string
      companyId?: string | null
      company?: {
        id: string
        name: string
        domain?: string | null
      } | null
    }
  }

  interface User {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
    role?: string
    companyId?: string | null
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
    role?: string
    companyId?: string | null
  }
}

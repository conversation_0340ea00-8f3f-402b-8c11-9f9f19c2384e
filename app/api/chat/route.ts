import { NextRequest } from "next/server";
import { sendToAIAgent } from "@/lib/api-client";
import { z } from "zod";

const ClientRequestSchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant']),
    content: z.string(),
  })),
  userId: z.string().optional(),
  sessionId: z.string().optional(),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { messages, userId, sessionId } = ClientRequestSchema.parse(body);

    const lastUserMessage = messages.filter(m => m.role === 'user').pop();
    if (!lastUserMessage) {
      return new Response("No user message found", { status: 400 });
    }

    const payload = {
      userId: userId || "guest_user",
      sessionId: sessionId || `session_${Date.now()}`,
      message: lastUserMessage.content,
    };

    const response = await sendToAIAgent(payload);

    return new Response(response.body, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-cache",
      },
    });

  } catch (error) {
    console.error("Chat API error:", error);

    if (error instanceof z.ZodError) {
      return new Response("Invalid request format", { status: 400 });
    }

    return new Response("Terjadi kesalahan. Silakan coba lagi.", { status: 500 });
  }
}

"use client";

import { useState } from "react";
import Chat from "@/components/Chat";
import Sidebar from "@/components/Sidebar";
import TopBar from "@/components/TopBar";
import ProtectedRoute from "@/components/ProtectedRoute";

export default function Home() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return (
    <ProtectedRoute>
      <main className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
        {/* Top Navigation Bar */}
        <TopBar onToggleSidebar={toggleSidebar} />

        {/* Main Content */}
        <div className="flex flex-1 overflow-hidden relative">
          {/* Sidebar */}
          <Sidebar isOpen={isSidebarOpen} onClose={closeSidebar} />

          {/* Chat Area */}
          <div className="flex-1 flex flex-col min-w-0">
            <Chat />
          </div>
        </div>
      </main>
    </ProtectedRoute>
  );
}

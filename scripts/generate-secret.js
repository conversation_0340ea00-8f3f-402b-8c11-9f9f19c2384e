#!/usr/bin/env node

/**
 * Generate a secure secret for NextAuth.js
 * Usage: node scripts/generate-secret.js
 */

const crypto = require('crypto');

function generateSecret() {
  return crypto.randomBytes(32).toString('base64');
}

const secret = generateSecret();

console.log('🔐 Generated NextAuth.js Secret:');
console.log('');
console.log(secret);
console.log('');
console.log('Add this to your .env.local file:');
console.log(`NEXTAUTH_SECRET="${secret}"`);
console.log('');
console.log('⚠️  Keep this secret secure and never commit it to version control!');

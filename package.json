{"name": "fin_chat", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.15.0", "@radix-ui/react-dropdown-menu": "^2.1.16", "ai": "^4.3.19", "lucide-react": "^0.542.0", "next": "15.5.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.15.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-textarea-autosize": "^8.5.9", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}